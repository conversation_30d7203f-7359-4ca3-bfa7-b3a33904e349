import requests

# API MEXC lấy danh sách hợp đồng
MEXC_API_URL = "https://contract.mexc.com/api/v1/contract/detail"

EXCLUDED_SYMBOLS = {'DOGE_USDT', 'SHIB_USDT','PEPE_USDT', 'USDC_USDT'}  # Ví dụ: loại trừ DOGE_USDT và SHIB_USDT

def get_contract_info():
    """L<PERSON>y danh sách hợp đồng từ API MEXC"""
    try:
        response = requests.get(MEXC_API_URL, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("success") and "data" in data:
            return data["data"]
        else:
            print("Lỗi dữ liệu trả về:", data)
            return []
    except requests.RequestException as e:
        print("Lỗi khi gọi API MEXC:", e)
        return []

def save_top_symbols():
    contracts = get_contract_info()
    if not contracts:
        print("❌ Không thể lấy dữ liệu từ MEXC!")
        return

    filtered_contracts = [
        contract for contract in contracts
        if contract["symbol"].endswith("USDT")
        and contract.get("maxLeverage", 0) >= 200
        and contract["symbol"] not in EXCLUDED_SYMBOLS
        and contract.get("contractSize", 0) <= 1000  
    ]

    sorted_contracts = sorted(filtered_contracts, key=lambda x: x.get("volume24", 0), reverse=True)[:20]

    top_symbols = [contract["symbol"] for contract in sorted_contracts]
    volume_factors = [contract["contractSize"] for contract in sorted_contracts]

    with open("symbol.py", "w") as f:
        f.write(f'MEXC_SYMBOL = {top_symbols}\n')
        f.write(f'VOLUME_FACTOR = {volume_factors}\n')

    print(f"✅ Đã lưu danh sách {len(top_symbols)} symbol USDT (maxLeverage >= 200, VOLUME_FACTOR <= 1000) vào symbol.py, loại trừ: {EXCLUDED_SYMBOLS}")

if __name__ == "__main__":
    save_top_symbols()