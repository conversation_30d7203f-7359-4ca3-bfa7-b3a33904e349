import requests
import logging
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import threading
import queue

# Cấu hình logger cho module telegram
logger = logging.getLogger('telegram')

class TelegramHandler:
    def __init__(self, token: str, admin_chat_id: str):
        self.token = token
        self.admin_chat_id = admin_chat_id
        self.base_url = f"https://api.telegram.org/bot{token}"
        self.command_handlers = {}  # Đăng ký các command handlers
        self.last_update_id = 0
        self.message_queue = queue.Queue()
        self.stop_polling = False
        self.polling_thread = None
        
        # Đăng ký các lệnh mặc định
        self.register_command('start', self._handle_start)
        self.register_command('help', self._handle_help)
        
        logger.info("TelegramHandler đã được khởi tạo")
    
    def register_command(self, command: str, handler: Callable):
        """Đăng ký một command handler"""
        self.command_handlers[command] = handler
        logger.debug(f"Đã đăng ký handler cho lệnh: {command}")
    
    def send_message(self, message: str, chat_id: Optional[str] = None) -> bool:
        """Gửi tin nhắn đến chat_id (mặc định là admin)"""
        if not self.token:
            logger.warning("Thiếu token Telegram")
            return False
            
        if not chat_id:
            chat_id = self.admin_chat_id
            
        url = f"{self.base_url}/sendMessage"
        payload = {"chat_id": chat_id, "text": message, "parse_mode": "HTML"}
        
        try:
            response = requests.post(url, json=payload, timeout=15)
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Telegram: {e}")
            return False
    
    def start_polling(self):
        """Bắt đầu polling thread để lắng nghe tin nhắn từ Telegram"""
        if self.polling_thread and self.polling_thread.is_alive():
            logger.warning("Polling thread đã đang chạy")
            return
            
        self.stop_polling = False
        self.polling_thread = threading.Thread(target=self._polling_worker, daemon=True)
        self.polling_thread.start()
        logger.info("Đã bắt đầu polling tin nhắn Telegram")
    
    def stop_polling_updates(self):
        """Dừng polling thread"""
        self.stop_polling = True
        if self.polling_thread:
            self.polling_thread.join(timeout=5)
            logger.info("Đã dừng polling tin nhắn Telegram")
    
    def _polling_worker(self):
        """Worker thread để liên tục poll các update từ Telegram"""
        while not self.stop_polling:
            try:
                updates = self._get_updates()
                if updates:
                    for update in updates:
                        if 'message' in update:
                            message = update['message']
                            self._process_message(message)
                time.sleep(1)  # Tránh quá tải API
            except Exception as e:
                logger.error(f"Lỗi trong polling thread: {e}")
                time.sleep(5)  # Nghỉ lâu hơn nếu có lỗi
    
    def _get_updates(self) -> List[Dict]:
        """Lấy các updates từ Telegram API"""
        try:
            params = {'offset': self.last_update_id + 1, 'timeout': 30}
            response = requests.get(f"{self.base_url}/getUpdates", params=params, timeout=35)
            if response.status_code == 200:
                data = response.json()
                if data['ok'] and data['result']:
                    self.last_update_id = max(update['update_id'] for update in data['result'])
                    return data['result']
            return []
        except Exception as e:
            logger.error(f"Lỗi khi lấy updates: {e}")
            return []
    
    def _process_message(self, message: Dict):
        """Xử lý tin nhắn nhận được từ Telegram"""
        chat_id = str(message['chat']['id'])
        
        # Bảo mật: chỉ chấp nhận tin nhắn từ admin
        if chat_id != self.admin_chat_id:
            logger.warning(f"Tin nhắn từ người dùng không được phép: {chat_id}")
            return
        
        if 'text' not in message:
            return
            
        text = message['text']
        if text.startswith('/'):
            # Xử lý lệnh
            command_parts = text[1:].split(' ', 1)
            command = command_parts[0].lower()
            args = command_parts[1] if len(command_parts) > 1 else ""
            
            if command in self.command_handlers:
                try:
                    self.command_handlers[command](chat_id, args, message)
                except Exception as e:
                    logger.error(f"Lỗi khi xử lý lệnh {command}: {e}")
                    self.send_message(f"⚠️ Lỗi xử lý lệnh: {str(e)}", chat_id)
            else:
                self.send_message(f"❓ Lệnh không được hỗ trợ: {command}", chat_id)
    
    def _handle_start(self, chat_id: str, args: str, message: Dict):
        """Handler cho lệnh /start"""
        user_name = message.get('from', {}).get('first_name', 'Trader')
        response = (
            f"🤖 <b>Chào mừng, {user_name}!</b>\n\n"
            f"Bot giao dịch đã sẵn sàng. Sử dụng /help để xem danh sách lệnh."
        )
        self.send_message(response, chat_id)
    
    def _handle_help(self, chat_id: str, args: str, message: Dict):
        """Handler cho lệnh /help"""
        commands = [
            "/status - Xem thông tin trạng thái bot và tài khoản",
            "/balance - Xem số dư và thông tin equity",
            "/orders - Xem danh sách lệnh đang mở",
            "/watchlist - Xem danh sách cặp đang theo dõi",
            "/help - Hiển thị menu trợ giúp này"
        ]
        response = "<b>📋 Danh sách lệnh:</b>\n" + "\n".join(commands)
        self.send_message(response, chat_id)

    # --- Handlers cho các chức năng quản lý giao dịch ---
    
    def handle_status(self, chat_id: str, args: str, message: Dict, get_status_callback: Callable = None):
        """Xử lý yêu cầu thông tin trạng thái"""
        if get_status_callback:
            status_info = get_status_callback()
            self.send_message(status_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)
    
    def handle_balance(self, chat_id: str, args: str, message: Dict, get_balance_callback: Callable = None):
        """Xử lý yêu cầu thông tin số dư"""
        if get_balance_callback:
            balance_info = get_balance_callback()
            self.send_message(balance_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)
    
    def handle_orders(self, chat_id: str, args: str, message: Dict, get_orders_callback: Callable = None):
        """Xử lý yêu cầu danh sách lệnh đang mở"""
        if get_orders_callback:
            orders_info = get_orders_callback()
            self.send_message(orders_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def handle_watchlist(self, chat_id: str, args: str, message: Dict, get_watchlist_callback: Callable = None):
        """Xử lý yêu cầu danh sách theo dõi"""
        if get_watchlist_callback:
            watchlist_info = get_watchlist_callback()
            self.send_message(watchlist_info, chat_id)
        else:
            self.send_message("❌ Chức năng đang được bảo trì", chat_id)

    def register_trading_commands(self, status_callback, balance_callback, orders_callback, watchlist_callback):
        """Đăng ký các callback xử lý cho từng lệnh liên quan đến giao dịch"""
        self.register_command('status', 
                              lambda chat_id, args, msg: self.handle_status(chat_id, args, msg, status_callback))
        self.register_command('balance', 
                              lambda chat_id, args, msg: self.handle_balance(chat_id, args, msg, balance_callback))
        self.register_command('orders', 
                              lambda chat_id, args, msg: self.handle_orders(chat_id, args, msg, orders_callback))
        self.register_command('watchlist', 
                              lambda chat_id, args, msg: self.handle_watchlist(chat_id, args, msg, watchlist_callback))