import pandas as pd
import numpy as np
# Make sure TA-Lib is installed and configured correctly
try:
    from talib import RSI, ATR, SMA # Import SMA directly if needed, though pandas_ta usually handles it
except ImportError:
    print("TA-Lib not found. Please install it: https://mrjbq7.github.io/ta-lib/install.html")
    # Consider exiting or raising an error if TA-Lib is essential
    raise ImportError("TA-Lib is required for this module.")
import pandas_ta as ta
import logging
from typing import Optional

# Setup logger for this module
logger = logging.getLogger(__name__)

def calculate_ha_rsi(df: pd.DataFrame, rsi_period: int = 14, smooth_period: int = 1) -> Optional[float]:
    """
    Calculates Heikin-Ashi smoothed RSI.

    Args:
        df: DataFrame with 'open', 'high', 'low', 'close' columns.
        rsi_period: The period for RSI calculation.
        smooth_period: The period for SMA smoothing of the HA-RSI.
                       If <= 1, no smoothing is applied.

    Returns:
        The calculated HA-RSI value as a float, or None if calculation fails.
    """
    try:
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in df.columns for col in required_cols):
            logger.error(f"Missing required columns for HA-RSI: Need {required_cols}, have {df.columns.tolist()}")
            return None

        # Need enough data for RSI + potential smoothing lookback + shifts
        min_data_needed = rsi_period + max(smooth_period, 1) + 2 # Add buffer for shifts/calcs
        if len(df) < min_data_needed:
            logger.warning(f"Not enough data for HA-RSI (need > {min_data_needed}, got {len(df)})")
            return None

        df_rsi = pd.DataFrame(index=df.index)
        # Calculate RSI for each OHLC component
        for col in required_cols:
            rsi_raw = RSI(df[col], timeperiod=rsi_period)
            df_rsi[col] = rsi_raw - 50 # Center around 0

        # Drop NaNs created by RSI calculation before HA calculation
        df_rsi.dropna(inplace=True)
        if df_rsi.empty:
             logger.warning("DataFrame empty after RSI calculation and NaN drop.")
             return None

        # Calculate Heikin-Ashi candles on the RSI data
        ha_df = pd.DataFrame(index=df_rsi.index)
        ha_df['close'] = df_rsi[['open', 'high', 'low', 'close']].mean(axis=1)
        ha_df['open'] = (df_rsi['open'].shift(1) + df_rsi['close'].shift(1)) / 2

        # Fill the first HA open value robustly
        if not ha_df.empty:
             ha_df['open'].iloc[0] = (df_rsi['open'].iloc[0] + df_rsi['close'].iloc[0]) / 2

        if ha_df.empty:
            logger.warning("HA DataFrame became empty unexpectedly.")
            return None

        ha_df['high'] = ha_df[['open', 'close']].join(df_rsi['high']).max(axis=1)
        ha_df['low'] = ha_df[['open', 'close']].join(df_rsi['low']).min(axis=1)

        # Drop NaNs created by the shift in HA open calculation
        ha_df.dropna(inplace=True)
        if ha_df.empty:
             logger.warning("HA DataFrame empty after NaN drop (shift).")
             return None

        # Calculate the final HA-RSI value (ohlc4)
        ha_rsi_series = ta.ohlc4(ha_df['open'], ha_df['high'], ha_df['low'], ha_df['close'])
        if ha_rsi_series is None or ha_rsi_series.empty:
             logger.warning("HA-RSI ohlc4 calculation resulted in empty series.")
             return None

        # --- FIX for TA_BAD_PARAM ---
        # Apply smoothing only if smooth_period > 1
        if smooth_period > 1:
            # Ensure enough data points in ha_rsi_series for SMA
            if len(ha_rsi_series.dropna()) >= smooth_period:
                 # Use pandas_ta wrapper which might handle edge cases better
                 ha_rsi_smoothed = ta.sma(ha_rsi_series, length=smooth_period)
                 # Check if smoothing was successful
                 if ha_rsi_smoothed is None or ha_rsi_smoothed.empty or pd.isna(ha_rsi_smoothed.iloc[-1]):
                      logger.warning(f"HA-RSI SMA({smooth_period}) smoothing failed or resulted in NaN.")
                      # Fallback to the last unsmoothed value if smoothing fails
                      if not pd.isna(ha_rsi_series.iloc[-1]):
                           ha_rsi_final = ha_rsi_series.iloc[-1]
                           logger.warning("Falling back to unsmoothed HA-RSI value.")
                      else:
                           logger.error("Unsmoothed HA-RSI value is also NaN.")
                           return None
                 else:
                      ha_rsi_final = ha_rsi_smoothed.iloc[-1]
            else:
                 logger.warning(f"Not enough data points ({len(ha_rsi_series.dropna())}) in HA-RSI series for SMA({smooth_period}). Using unsmoothed value.")
                 if not pd.isna(ha_rsi_series.iloc[-1]):
                      ha_rsi_final = ha_rsi_series.iloc[-1]
                 else:
                      logger.error("Unsmoothed HA-RSI value is NaN when attempting fallback.")
                      return None
        else:
            # If smooth_period <= 1, use the last unsmoothed value directly
            if pd.isna(ha_rsi_series.iloc[-1]):
                 logger.error(f"Unsmoothed HA-RSI value is NaN (smooth_period={smooth_period}).")
                 return None
            ha_rsi_final = ha_rsi_series.iloc[-1]
            # logger.debug(f"Using unsmoothed HA-RSI (smooth_period={smooth_period}).")


        if pd.isna(ha_rsi_final):
             logger.error("Final HA-RSI value is NaN after processing.")
             return None

        # logger.info(f"Calculated HA-RSI: {ha_rsi_final:.2f}")
        return float(ha_rsi_final) # Ensure float type

    except Exception as e:
        # Log the full traceback for detailed debugging
        logger.error(f"Error calculating HA-RSI: {e}", exc_info=True)
        return None

def calculate_atr(df: pd.DataFrame, period: int = 14) -> Optional[float]:
    """
    Calculates Average True Range (ATR).

    Args:
        df: DataFrame with 'high', 'low', 'close' columns.
        period: The period for ATR calculation.

    Returns:
        The calculated ATR value as a float, or None if calculation fails.
    """
    try:
        required_cols = ['high', 'low', 'close']
        if not all(col in df.columns for col in required_cols):
             logger.error(f"Missing required columns for ATR: Need {required_cols}, have {df.columns.tolist()}")
             return None
        # TA-Lib ATR needs period + 1 data points to start outputting non-NaNs?
        # Let's require at least period + 1 for safety.
        if len(df) < period + 1:
             logger.warning(f"Not enough data for ATR (need >= {period + 1}, got {len(df)})")
             return None

        atr_values = ATR(df['high'], df['low'], df['close'], timeperiod=period)

        # Check if the series is empty or the last value is NaN
        if atr_values.empty or pd.isna(atr_values.iloc[-1]):
             logger.warning(f"ATR calculation resulted in empty or NaN last value (len={len(atr_values)}).")
             # Try accessing second to last if last is NaN and series has >= 2 non-NaNs before the end
             valid_atr = atr_values.dropna()
             if len(valid_atr) >= 1:
                  last_valid_atr = valid_atr.iloc[-1]
                  logger.warning(f"Using last valid ATR value: {last_valid_atr:.5f}")
                  return float(last_valid_atr)
             else:
                  logger.error("No valid ATR values found in the series.")
                  return None # No valid value found

        return float(atr_values.iloc[-1]) # Ensure float type
    except Exception as e:
        logger.error(f"Error calculating ATR: {e}", exc_info=True)
        return None

