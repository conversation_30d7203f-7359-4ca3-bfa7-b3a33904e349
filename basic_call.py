import hashlib
import json
import time
from curl_cffi import requests
import yaml
import logging

# <PERSON>hi<PERSON><PERSON> lập logging
logger = logging.getLogger('api')
logger.setLevel(logging.DEBUG)
handler = logging.handlers.RotatingFileHandler('logs/api.log', maxBytes=10*1024*1024, backupCount=5)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

with open("config.yaml", "r") as file:
    config = yaml.safe_load(file)

SIM_KEY = config["SIM_KEY"]
ORDER_URL = 'https://futures.mexc.com/api/v1/private/order/create'

def build_sign_from_key(payload):
    def hash_md5(value):
        return hashlib.md5(value.encode('utf-8')).hexdigest()

    date_now = str(int(time.time() * 1000))
    g = hash_md5(SIM_KEY + date_now)[7:]
    s = json.dumps(payload, separators=(',', ':'))
    sign = hash_md5(date_now + s + g)
    return {'time': date_now, 'sign': sign}

def post_method(obj):
    signature = build_sign_from_key(obj)
    headers = {
        'Content-Type': 'application/json',
        'x-mxc-sign': signature['sign'],
        'x-mxc-nonce': signature['time'],
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36',
        'Authorization': SIM_KEY
    }
    
    logger.debug(f"Gửi yêu cầu API: URL={ORDER_URL}, Payload={obj}, Headers={headers}")
    
    try:
        response = requests.post(ORDER_URL, headers=headers, json=obj)
        logger.debug(f"Phản hồi API: Status={response.status_code}, Content={response.text}")
        
        try:
            result = response.json()
        except ValueError:
            logger.error(f"Không thể parse JSON từ phản hồi: {response.text}")
            return {"code": -1, "msg": "Phản hồi API không phải JSON hợp lệ"}
            
        return result
    except requests.RequestException as e:
        logger.error(f"Lỗi khi gửi yêu cầu API: {str(e)}")
        return {"code": -1, "msg": f"Lỗi kết nối: {str(e)}"}

def place_order(symbol, price, tp, sl, volume, leverage, is_market, is_long):
    # Ghi log tham số đầu vào
    logger.debug(f"Đặt lệnh: symbol={symbol}, price={price}, tp={tp}, sl={sl}, volume={volume}, "
                 f"leverage={leverage}, is_market={is_market}, is_long={is_long}")
    
    # Kiểm tra tham số cơ bản
    if not symbol or volume <= 0 or price <= 0:
        logger.error(f"Tham số không hợp lệ: symbol={symbol}, volume={volume}, price={price}")
        return False, {"code": -1, "msg": "Tham số không hợp lệ"}
    
    # Chuyển đổi các giá trị số thành chuỗi với định dạng phù hợp
    price_str = str(price)
    tp_str = str(tp)
    sl_str = str(sl)
    volume_str = str(volume)
    leverage_str = str(leverage)
    
    obj = {
        'symbol': symbol,
        'price': price_str,
        'vol': volume_str,
        'openType': 2,
        'side': 1 if is_long else 3,
        'type': "5" if is_market else "1",
        'leverage': leverage_str,
        'takeProfitPrice': tp_str,
        'stopLossPrice': sl_str,  
    }
    
    logger.info(f"Chuẩn bị gửi lệnh: {obj}")
    result = post_method(obj)
    
    # Ghi log kết quả
    logger.debug(f"Kết quả API: {result}")
    
    # Kiểm tra kết quả và trả về
    if result.get("code") == 0:
        logger.info(f"Đặt lệnh thành công: {result}")
        return True, result
    else:
        error_msg = result.get("msg", "Lỗi API không xác định")
        logger.error(f"Không thể đặt lệnh: {error_msg}, Kết quả đầy đủ: {result}")
        return False, result